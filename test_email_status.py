#!/usr/bin/env python3
"""
Test script to check email status in allowed_emails and users tables
"""
import asyncio
import sys
import os
from dotenv import load_dotenv

# Add the backend directory to Python path
sys.path.append('.')

# Load environment variables
load_dotenv()

async def test_email_status():
    """Test email status checking"""
    try:
        from app.services.auth_utils import check_email_and_user_status
        
        # Test with the email from your screenshot
        test_email = "<EMAIL>"
        
        print(f"Testing email status for: {test_email}")
        print("=" * 50)
        
        result = await check_email_and_user_status(test_email)
        print(f"✅ Success: {result}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e)}")
        if hasattr(e, 'status_code'):
            print(f"Status code: {e.status_code}")
        if hasattr(e, 'detail'):
            print(f"Detail: {e.detail}")

async def test_environment_vars():
    """Test environment variables"""
    print("\n" + "=" * 50)
    print("Environment Variables Check:")
    print("=" * 50)
    
    vars_to_check = [
        "SUPABASE_URL",
        "SUPABASE_PUBLIC_KEY", 
        "SUPABASE_SERVICE_KEY"
    ]
    
    for var in vars_to_check:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: Set (length: {len(value)})")
        else:
            print(f"❌ {var}: Not set")

async def main():
    """Run all tests"""
    await test_environment_vars()
    await test_email_status()

if __name__ == "__main__":
    asyncio.run(main())
