// API URL configuration
const BASE_URL = 'http://localhost:8000';
// const BASE_URL = process.env.REACT_APP_API_URL

const API_URLS = {
  // User endpoints
  USER: {
    PROFILE: `${BASE_URL}/user/profile`,
    UPDATE_PROFILE: `${BASE_URL}/user/update`,
    UPDATE_USER: (userId) => `${BASE_URL}/user/users/${userId}`,
    RECENT_ACTIVITY: `${BASE_URL}/user/recent-activity`,
    CHECK_USER_EXISTS: (email) => `${BASE_URL}/user/check-status/${email}`,
  },
  // Organization endpoints
  ORGANIZATION: {
    DETAILS: `${BASE_URL}/organization/details`,
  },
  // SOP endpoints
  SOP: {
    LIST: `${BASE_URL}/sops/`,
    UPLOAD: `${BASE_URL}/sops/upload`,
    DELETE: `${BASE_URL}/sops`,
    DOWNLOAD: `${BASE_URL}/sops/download`,
  },
  // Regulations endpoints
  REGULATIONS: {
    LIST: `${BASE_URL}/regulations/`,
    ORGANIZATION: `${BASE_URL}/regulations/organization`,
  },
  // Analysis endpoints
  ANALYSIS: {
    RESULTS: `${BASE_URL}/analysis/results`,
    ANALYZE: `${BASE_URL}/analysis/analyze`
  },
  // Other API endpoints can be added here
  SOPS: {
    GET_ALL: '/sops',
    GET: '/sops',
    CREATE: '/sops',
    UPDATE: '/sops',
    DELETE: '/sops',
    DOWNLOAD: '/sops/download'
  },

  ANALYSIS_RESULTS: {
    UPDATE_META:`${BASE_URL}/analysis/results`
  },

  DEPARTMENTS: {
    LIST: `${BASE_URL}/sops/departments`
  },
    CHAT: {
    QUERY: `${BASE_URL}/chat/query`
  },

};

export default API_URLS; 