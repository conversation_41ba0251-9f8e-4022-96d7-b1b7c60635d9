#!/usr/bin/env python3
"""
Test script to verify the logging foreign key constraint fix
"""
import asyncio
import uuid
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'latent-backend'))

async def test_logging_sequence():
    """Test the logging sequence that was causing foreign key constraint errors"""
    try:
        from app.services.error_logger import log_user_request, log_interaction
        
        # Generate a test request ID
        test_request_id = str(uuid.uuid4())
        print(f"Testing with request_id: {test_request_id}")
        
        # Test the sequence that was failing
        print("\n=== Testing log_user_request followed by log_interaction ===")
        
        # First log the user request (this creates the record in user_requests table)
        await log_user_request(
            user_id="test_user",
            user_email="<EMAIL>",
            ip_address="127.0.0.1",
            endpoint="/api/regulations/organization",
            method="GET",
            request_params={"user_id": "test_user"},
            request_id=test_request_id
        )
        print("✅ User request logged successfully")
        
        # Then log the interaction (this should now work without foreign key constraint error)
        await log_interaction(
            request_id=test_request_id,
            step="test_step",
            description="Testing step after user request",
            params={"test": "param"}
        )
        print("✅ Interaction logged successfully")
        
        # Test multiple interactions with the same request_id
        await log_interaction(
            request_id=test_request_id,
            step="test_step_2",
            description="Second test step",
            params={"test": "param2"}
        )
        print("✅ Second interaction logged successfully")
        
        print("\n🎉 All tests passed! The foreign key constraint issue has been fixed.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing the logging foreign key constraint fix...")
    asyncio.run(test_logging_sequence())
