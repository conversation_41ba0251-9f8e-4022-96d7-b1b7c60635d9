from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any, Text
import httpx
import jwt
import os
import uuid
import asyncio
from dotenv import load_dotenv
from app.services.auth_utils import verify_token, check_email_and_user_status
from app.services.error_logger import log_user_request, log_interaction
# Load environment variables
load_dotenv()

# Get Supabase URL from environment variables (not the service key)
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_JWT_SECRET = os.getenv("SUPABASE_JWT_SECRET")  # JWT secret for verifying tokens

# Security scheme for JWT
security = HTTPBearer()

router = APIRouter()


# Get current user profile
@router.get("/profile")
async def get_profile(request: Request, payload: Dict[Any, Any] = Depends(verify_token)):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function - use await to ensure user_request record exists first
    await log_interaction(request_id, "get_profile_start", f"Starting get_profile for user: {user_id}", params=request_params)

    # Extract user ID from the JWT token
    if not user_id:
        asyncio.create_task(log_interaction(request_id, "invalid_user_session", "Invalid or expired user session", status="error"))
        raise HTTPException(status_code=401, detail="Your session is invalid or expired. Please log in again.")

    print(f"User ID from token: {user_id}")
    asyncio.create_task(log_interaction(request_id, "user_id_extracted", f"Extracted user ID from token: {user_id}", params={"user_id": user_id}))

    # Make request to Supabase with the user's token
    # This will respect RLS policies
    asyncio.create_task(log_interaction(request_id, "fetch_user_profile_attempt", f"Fetching user profile: {user_id}", params={"user_id": user_id}))

    async with httpx.AsyncClient() as client:
        # First, try to get the specific user's profile
        response = await client.get(
            f"{SUPABASE_URL}/rest/v1/users",
            headers={
                "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),  # Public key from .env
                "Authorization": f"Bearer {payload.get('access_token', '')}",
                "Content-Type": "application/json"
            },
            params={
                "select": "*,organization:organization_id(*)",
                "id": f"eq.{user_id}"
            }
        )
        
        print(f"Supabase response status: {response.status_code}")

        if response.status_code != 200:
            print(f"Error response: {response.text}")
            asyncio.create_task(log_interaction(request_id, "fetch_user_profile_failure", f"Failed to fetch user profile: {user_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(
                status_code=response.status_code,
                detail="We couldn't retrieve your user profile. Please try again or contact support."
            )

        print("--------------------------------")

        user_data = response.json()
        print(f"User data response: {user_data}")

        if not user_data or len(user_data) == 0:
            print(f"User not found")
            asyncio.create_task(log_interaction(request_id, "user_profile_not_found", f"User profile not found: {user_id}",
                                              status="error", params={"user_id": user_id}))
            return {"error": "User not found"}

        asyncio.create_task(log_interaction(request_id, "fetch_user_profile_success", f"Successfully fetched user profile: {user_id}",
                                          response={"user_id": user_id, "has_organization": bool(user_data[0].get("organization"))}))
        
        # Now try to access all users to test if RLS is working
        asyncio.create_task(log_interaction(request_id, "rls_testing_start", "Starting RLS (Row Level Security) testing"))

        try:
            # First test with public key and user token (RLS should be enforced)
            asyncio.create_task(log_interaction(request_id, "rls_public_key_test_attempt", "Testing RLS with public key and user token"))

            all_users_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers={
                    "apikey": os.getenv("SUPABASE_PUBLIC_KEY") or "",
                    "Authorization": f"Bearer {payload.get('access_token', '')}",
                    "Content-Type": "application/json"
                },
                params={"select": "*"}
            )

            print(f"Public key users response status: {all_users_response.status_code}")
            public_key_users = all_users_response.json()
            print("Public key users: ", public_key_users)

            asyncio.create_task(log_interaction(request_id, "rls_public_key_test_result", "RLS public key test completed",
                                              response={"status_code": all_users_response.status_code, "users_count": len(public_key_users)}))

            # Now test with secret key (bypasses RLS)
            asyncio.create_task(log_interaction(request_id, "rls_secret_key_test_attempt", "Testing RLS bypass with secret key"))

            supabase_secret_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or ""
            secret_key_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers={
                    "apikey": supabase_secret_key,
                    "Authorization": f"Bearer {supabase_secret_key}",
                    "Content-Type": "application/json"
                },
                params={"select": "*"}
            )

            print(f"Secret key users response status: {secret_key_response.status_code}")
            secret_key_users = secret_key_response.json()
            print(secret_key_users)

            asyncio.create_task(log_interaction(request_id, "rls_secret_key_test_result", "RLS secret key test completed",
                                              response={"status_code": secret_key_response.status_code, "users_count": len(secret_key_users)}))
            
            # Add RLS test results to the response
            rls_working = len(secret_key_users) > len(public_key_users)
            user_data[0]["rls_test"] = {
                "success": True,
                "public_key_users_count": len(public_key_users),
                "secret_key_users_count": len(secret_key_users),
                "rls_working": rls_working
            }

            asyncio.create_task(log_interaction(request_id, "rls_testing_success", "RLS testing completed successfully",
                                              response={"rls_working": rls_working, "public_users": len(public_key_users), "secret_users": len(secret_key_users)}))

            print("Established that we are scanning entire DB with service key and only getting own user data with user token")
        except Exception as e:
            print(f"Failed to establish that we are scanning entire DB with service key and only getting own user data with user token")
            print(f"Error: {e}")
            asyncio.create_task(log_interaction(request_id, "rls_testing_failure", "RLS testing failed",
                                              status="error", response={"error": str(e)}))

        asyncio.create_task(log_interaction(request_id, "get_profile_success", f"Successfully completed get_profile for user: {user_id}",
                                          response={"user_id": user_id, "profile_retrieved": True}))

        return user_data

# List all users in the user's organization (respecting RLS)
@router.get("/organization/users")
async def list_organization_users(request: Request, payload: Dict[Any, Any] = Depends(verify_token)):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function - use await to ensure user_request record exists first
    await log_interaction(request_id, "list_org_users_start", f"Starting list_organization_users for user: {user_id}", params=request_params)

    user_token = payload.get("access_token", "")

    # Make request to Supabase with the user's token
    # RLS will ensure they only see users from their organization
    asyncio.create_task(log_interaction(request_id, "fetch_org_users_attempt", f"Fetching organization users for user: {user_id}", params={"user_id": user_id}))

    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{SUPABASE_URL}/rest/v1/users",
            headers={
                "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),  # Public key from .env
                "Authorization": f"Bearer {user_token}",
                "Content-Type": "application/json"
            },
            params={"select": "id,email,first_name,last_name,org_role"}
        )

        print(f"Organization users response status: {response.status_code}")

        if response.status_code != 200:
            print(f"Error response: {response.text}")
            asyncio.create_task(log_interaction(request_id, "fetch_org_users_failure", f"Failed to fetch organization users: {user_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(
                status_code=response.status_code,
                detail="We couldn't retrieve the list of organization users. Please try again or contact support."
            )

        data = response.json()
        print(f"Organization users response: {data}")

        asyncio.create_task(log_interaction(request_id, "list_org_users_success", f"Successfully retrieved organization users for user: {user_id}",
                                          response={"org_users_count": len(data), "user_id": user_id}))

        return data

@router.patch("/users/{user_id}")
async def update_user(
    request: Request,
    user_id: str,
    user_update: Dict[str, Any],
    payload: Dict[Any, Any] = Depends(verify_token)
):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    current_user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"target_user_id": user_id, "current_user_id": current_user_id, "update_fields": list(user_update.keys())}

    # Log the user request
    await log_user_request(
        current_user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function - use await to ensure user_request record exists first
    await log_interaction(request_id, "update_user_start", f"Starting update_user for user: {user_id}", params=request_params)

    user_token = payload.get("access_token", "")

    # Validate that the update only contains allowed fields
    allowed_fields = {"first_name", "last_name", "metadata"}
    update_fields = set(user_update.keys())

    asyncio.create_task(log_interaction(request_id, "validate_update_fields", f"Validating update fields for user: {user_id}",
                                      params={"allowed_fields": list(allowed_fields), "provided_fields": list(update_fields)}))

    if not update_fields.issubset(allowed_fields):
        asyncio.create_task(log_interaction(request_id, "invalid_update_fields", f"Invalid update fields provided for user: {user_id}",
                                          status="error", response={"invalid_fields": list(update_fields - allowed_fields)}))
        raise HTTPException(
            status_code=400,
            detail="You can only update your first name, last name, or profile information."
        )
    
    # Handle metadata separately if it exists in the update
    data_to_update = user_update.copy()
    if "metadata" in data_to_update:
        asyncio.create_task(log_interaction(request_id, "metadata_merge_start", f"Starting metadata merge for user: {user_id}",
                                          params={"new_metadata": data_to_update["metadata"]}))

        # Get current user data to merge metadata properly
        async with httpx.AsyncClient() as client:
            get_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers={
                    "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),
                    "Authorization": f"Bearer {user_token}",
                    "Content-Type": "application/json"
                },
                params={"id": f"eq.{user_id}", "select": "metadata"}
            )

            if get_response.status_code == 200:
                current_data = get_response.json()
                if current_data and len(current_data) > 0:
                    current_metadata = current_data[0].get("metadata", {}) or {}
                    # Merge the existing metadata with the new metadata
                    updated_metadata = {**current_metadata, **data_to_update["metadata"]}
                    data_to_update["metadata"] = updated_metadata

                    asyncio.create_task(log_interaction(request_id, "metadata_merge_success", f"Successfully merged metadata for user: {user_id}",
                                                      response={"merged_metadata_keys": list(updated_metadata.keys())}))
                else:
                    asyncio.create_task(log_interaction(request_id, "metadata_merge_no_existing", f"No existing metadata found for user: {user_id}"))
            else:
                asyncio.create_task(log_interaction(request_id, "metadata_fetch_failure", f"Failed to fetch existing metadata for user: {user_id}",
                                                  status="error", response={"status_code": get_response.status_code}))
    
    # Make request to Supabase with the user's token
    asyncio.create_task(log_interaction(request_id, "update_user_attempt", f"Attempting to update user: {user_id}",
                                      params={"user_id": user_id, "update_data": data_to_update}))

    async with httpx.AsyncClient() as client:
        response = await client.patch(
            f"{SUPABASE_URL}/rest/v1/users",
            headers={
                "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),
                "Authorization": f"Bearer {user_token}",
                "Content-Type": "application/json",
                "Prefer": "return=representation"  # Ensure Supabase returns the updated record
            },
            params={"id": f"eq.{user_id}"},
            json=data_to_update
        )

        # Accept both 200 (OK) and 204 (No Content) as success
        if response.status_code == 204:
            # No content returned, but update succeeded
            asyncio.create_task(log_interaction(request_id, "update_user_success_no_content", f"User updated successfully (no content): {user_id}",
                                              response={"user_id": user_id, "status_code": 204}))
            return {"message": "User updated successfully, but no content was returned."}
        elif response.status_code == 200:
            asyncio.create_task(log_interaction(request_id, "update_user_success", f"User updated successfully: {user_id}",
                                              response={"user_id": user_id, "updated_data": response.json()}))
            return response.json()
        else:
            asyncio.create_task(log_interaction(request_id, "update_user_failure", f"Failed to update user: {user_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(
                status_code=response.status_code,
                detail="We couldn't update your user profile. Please try again or contact support."
            )



@router.patch("/organizations/{organization_id}/metadata")
async def update_organization_metadata(
    request: Request,
    organization_id: str,
    metadata_update: Dict[str, Any],
    payload: Dict[Any, Any] = Depends(verify_token)
):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"organization_id": organization_id, "user_id": user_id, "metadata_keys": list(metadata_update.keys())}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function - use await to ensure user_request record exists first
    await log_interaction(request_id, "update_org_metadata_start", f"Starting update_organization_metadata for org: {organization_id}", params=request_params)

    user_token = payload.get("access_token", "")

    # Make request to Supabase with the user's token
    asyncio.create_task(log_interaction(request_id, "update_org_metadata_attempt", f"Attempting to update organization metadata: {organization_id}",
                                      params={"organization_id": organization_id, "metadata_update": metadata_update}))

    async with httpx.AsyncClient() as client:
        response = await client.patch(
            f"{SUPABASE_URL}/rest/v1/organizations",
            headers={
                "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),
                "Authorization": f"Bearer {user_token}",
                "Content-Type": "application/json",
                "Prefer": "return=representation"  # Ensure Supabase returns the updated record
            },
            params={"id": f"eq.{organization_id}"},
            json={"metadata": metadata_update}
        )

        # Accept both 200 (OK) and 204 (No Content) as success, but handle 204 by returning a message
        if response.status_code == 204:
            # No content returned, but update succeeded
            asyncio.create_task(log_interaction(request_id, "update_org_metadata_success_no_content", f"Organization metadata updated successfully (no content): {organization_id}",
                                              response={"organization_id": organization_id, "status_code": 204}))
            return {"message": "Organization metadata updated successfully, but no content was returned."}
        elif response.status_code == 200:
            asyncio.create_task(log_interaction(request_id, "update_org_metadata_success", f"Organization metadata updated successfully: {organization_id}",
                                              response={"organization_id": organization_id, "updated_data": response.json()}))
            return response.json()
        else:
            asyncio.create_task(log_interaction(request_id, "update_org_metadata_failure", f"Failed to update organization metadata: {organization_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(
                status_code=response.status_code,
                detail="We couldn't update the organization metadata. Please try again or contact support."
            )

@router.get("/recent-activity")
async def get_recent_activity(request: Request, payload: Dict[Any, Any] = Depends(verify_token)):
    """
    Get recent activity for the user's organization including:
    - Last 5 SOPs uploaded by users in the organization
    - Last 5 gap analyses done by the organization
    Returns combined chronological activity feed of last 5 activities
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function - use await to ensure user_request record exists first
    await log_interaction(request_id, "get_recent_activity_start", f"Starting get_recent_activity for user: {user_id}", params=request_params)

    # Log payload structure for debugging
    payload_debug = {
        "has_sub": "sub" in payload,
        "has_email": "email" in payload,
        "has_access_token": "access_token" in payload,
        "payload_keys": list(payload.keys())
    }
    asyncio.create_task(log_interaction(request_id, "payload_debug", "Payload structure analysis", params=payload_debug))

    try:
        user_token = payload.get("access_token", "")

        # Enhanced debugging for 401 issues
        print(f"DEBUG - User ID: {user_id}")
        print(f"DEBUG - User Email: {user_email}")
        print(f"DEBUG - Token exists: {bool(user_token)}")
        print(f"DEBUG - Token length: {len(user_token) if user_token else 0}")

        if not user_id:
            asyncio.create_task(log_interaction(request_id, "missing_user_id", "User ID not found in token",
                                              status="error", params={"payload_keys": list(payload.keys())}))
            raise HTTPException(status_code=401, detail="User ID not found in token")

        if not user_token:
            asyncio.create_task(log_interaction(request_id, "missing_access_token", "Access token not found in payload",
                                              status="error", params={"payload_keys": list(payload.keys())}))
            raise HTTPException(status_code=401, detail="Access token not found")

        print(f"Fetching recent activity for user: {user_id}")
        asyncio.create_task(log_interaction(request_id, "fetching_recent_activity", f"Fetching recent activity for user: {user_id}"))

        async with httpx.AsyncClient() as client:
            # Get user's organization ID first
            user_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers={
                    "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),
                    "Authorization": f"Bearer {user_token}",
                    "Content-Type": "application/json"
                },
                params={
                    "select": "organization_id",
                    "id": f"eq.{user_id}"
                }
            )

            if user_response.status_code != 200:
                print(f"Error fetching user data: {user_response.text}")
                print(f"DEBUG - Response status: {user_response.status_code}")
                print(f"DEBUG - Response headers: {dict(user_response.headers)}")

                # Log detailed error information
                error_details = {
                    "status_code": user_response.status_code,
                    "error": user_response.text,
                    "user_id": user_id,
                    "token_length": len(user_token) if user_token else 0,
                    "supabase_url": SUPABASE_URL
                }

                asyncio.create_task(log_interaction(request_id, "fetch_user_org_failure",
                                                  f"Failed to fetch user organization for user: {user_id}",
                                                  status="error", response=error_details))

                # Return more specific error message based on status code
                if user_response.status_code == 401:
                    raise HTTPException(status_code=401, detail="Authentication failed - token may be expired or invalid")
                elif user_response.status_code == 403:
                    raise HTTPException(status_code=403, detail="Access denied - insufficient permissions")
                else:
                    raise HTTPException(
                        status_code=user_response.status_code,
                        detail=f"Failed to fetch user organization (Status: {user_response.status_code})"
                    )

            user_data = user_response.json()
            if not user_data:
                asyncio.create_task(log_interaction(request_id, "user_not_found", f"User not found: {user_id}", status="error"))
                raise HTTPException(status_code=404, detail="User not found")

            organization_id = user_data[0].get("organization_id")
            if not organization_id:
                asyncio.create_task(log_interaction(request_id, "no_organization", f"User has no organization: {user_id}", status="error"))
                raise HTTPException(status_code=400, detail="User has no organization")

            print(f"Fetching activity for organization: {organization_id}")
            asyncio.create_task(log_interaction(request_id, "fetching_org_activity", f"Fetching activity for organization: {organization_id}"))

            # Fetch recent SOPs with user information
            sops_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/sops",
                headers={
                    "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),
                    "Authorization": f"Bearer {user_token}",
                    "Content-Type": "application/json"
                },
                params={
                    "select": "id,title,created_at,uploaded_by,users!uploaded_by(first_name,last_name)",
                    "organization_id": f"eq.{organization_id}",
                    "is_deleted": "eq.false",
                    "order": "created_at.desc",
                    "limit": "5"
                }
            )

            if sops_response.status_code != 200:
                print(f"Error fetching SOPs: {sops_response.text}")
                asyncio.create_task(log_interaction(request_id, "fetch_sops_failure", f"Failed to fetch recent SOPs for org: {organization_id}", status="error", response={"status_code": sops_response.status_code, "error": sops_response.text}))
                raise HTTPException(
                    status_code=sops_response.status_code,
                    detail="Failed to fetch recent SOPs"
                )

            sops_data = sops_response.json()
            print(f"Fetched {len(sops_data)} recent SOPs")
            asyncio.create_task(log_interaction(request_id, "fetch_sops_success", f"Fetched recent SOPs for org: {organization_id}", response={"sops": sops_data}))

            # Fetch recent gap analyses with SOP and regulation information
            gap_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                headers={
                    "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),
                    "Authorization": f"Bearer {user_token}",
                    "Content-Type": "application/json"
                },
                params={
                    "select": "id,analyzed_at,metadata,sops!sop_id(id,title,organization_id)",
                    "order": "analyzed_at.desc",
                    "limit": "5"
                }
            )

            if gap_response.status_code != 200:
                print(f"Error fetching gap analyses: {gap_response.text}")
                asyncio.create_task(log_interaction(request_id, "fetch_gap_analyses_failure", f"Failed to fetch recent gap analyses for org: {organization_id}", status="error", response={"status_code": gap_response.status_code, "error": gap_response.text}))
                raise HTTPException(
                    status_code=gap_response.status_code,
                    detail="Failed to fetch recent gap analyses"
                )

            gap_data = gap_response.json()
            print(f"Fetched {len(gap_data)} recent gap analyses")
            asyncio.create_task(log_interaction(request_id, "fetch_gap_analyses_success", f"Fetched recent gap analyses for org: {organization_id}", response={"gap_analyses": gap_data}))

            # Filter gap analyses to only include those from the user's organization
            org_gap_data = []
            for gap in gap_data:
                sop_info = gap.get("sops")
                if sop_info and sop_info.get("organization_id") == organization_id:
                    org_gap_data.append(gap)

            print(f"Filtered to {len(org_gap_data)} gap analyses from organization")
            asyncio.create_task(log_interaction(request_id, "filter_gap_analyses_org", f"Filtered gap analyses to organization: {organization_id}", response={"org_gap_analyses": org_gap_data}))

            # Get regulation names for gap analyses
            all_regulation_ids = set()
            for gap in org_gap_data:
                metadata = gap.get("metadata", {})
                reg_ids = metadata.get("regulation_ids", [])
                all_regulation_ids.update(reg_ids)

            regulation_names = {}
            if all_regulation_ids:
                formatted_reg_ids = ','.join([f'"{reg_id}"' for reg_id in all_regulation_ids])
                reg_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/regulations",
                    headers={
                        "apikey": os.getenv("SUPABASE_PUBLIC_KEY"),
                        "Authorization": f"Bearer {user_token}",
                        "Content-Type": "application/json"
                    },
                    params={
                        "select": "id,name",
                        "id": f"in.({formatted_reg_ids})"
                    }
                )

                if reg_response.status_code == 200:
                    regulations = reg_response.json()
                    regulation_names = {reg["id"]: reg["name"] for reg in regulations}
                    print(f"Fetched {len(regulation_names)} regulation names")
                    asyncio.create_task(log_interaction(request_id, "fetch_regulation_names_success", f"Fetched regulation names for org: {organization_id}", response={"regulation_names": regulation_names}))
                else:
                    asyncio.create_task(log_interaction(request_id, "fetch_regulation_names_failure", f"Failed to fetch regulation names for org: {organization_id}", status="error", response={"status_code": reg_response.status_code, "error": reg_response.text}))

            # Create activity feed
            activities = []

            # Add SOP activities
            for sop in sops_data:
                user_info = sop.get("users", {})
                user_name = f"{user_info.get('first_name', '')} {user_info.get('last_name', '')}".strip()
                if not user_name:
                    user_name = "Unknown User"

                activities.append({
                    "title": f"{user_name} added SOP - {sop['title']}",
                    "timestamp": sop["created_at"]
                })

            # Add gap analysis activities
            for gap in org_gap_data:
                sop_info = gap.get("sops", {})
                sop_title = sop_info.get("title", "Unknown SOP")

                metadata = gap.get("metadata", {})
                reg_ids = metadata.get("regulation_ids", [])
                reg_names = [regulation_names.get(reg_id, f"Regulation {reg_id}") for reg_id in reg_ids]
                regulations_text = ", ".join(reg_names) if reg_names else "Unknown Regulations"

                activities.append({
                    "title": f"Gap analysis done on {sop_title} based on {regulations_text}",
                    "timestamp": gap["analyzed_at"]
                })

            # Sort activities by timestamp (most recent first) and take last 5
            activities.sort(key=lambda x: x["timestamp"], reverse=True)
            recent_activities = activities[:5]

            print(f"Returning {len(recent_activities)} recent activities")
            asyncio.create_task(log_interaction(request_id, "get_recent_activity_success", f"Successfully fetched recent activities for org: {organization_id}", response={"recent_activities": recent_activities}))

            return {
                "recent_activities": recent_activities,
            }

    except HTTPException as http_exc:
        asyncio.create_task(log_interaction(request_id, "get_recent_activity_http_error", f"HTTPException in get_recent_activity for user: {user_id}", status="error", response={"detail": str(http_exc.detail), "status_code": http_exc.status_code}))
        raise
    except Exception as e:
        print(f"Error in recent activity endpoint: {str(e)}")
        asyncio.create_task(log_interaction(request_id, "get_recent_activity_exception", f"Unexpected exception in get_recent_activity for user: {user_id}", status="error", response={"error": str(e)}))
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch recent activity"
        )


@router.get("/check-status/{email}")
async def check_user_status_route(request: Request, email: str):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"email": email}

    # Log the user request (no user_id available for this endpoint)
    await log_user_request(
        None, email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function - use await to ensure user_request record exists first
    await log_interaction(request_id, "check_user_status_start", f"Starting check_user_status for email: {email}", params=request_params)

    try:
        asyncio.create_task(log_interaction(request_id, "check_email_status_attempt", f"Checking email and user status: {email}", params={"email": email}))
        result = await check_email_and_user_status(email)
        asyncio.create_task(log_interaction(request_id, "check_user_status_success", f"Successfully checked user status: {email}",
                                          response={"email": email, "is_allowed": result.get("is_allowed"), "user_exists": result.get("user_exists")}))
        return result
    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "check_user_status_http_error", f"HTTPException in check_user_status: {email}",
                                          status="error", response={"detail": str(http_error.detail), "status_code": http_error.status_code}))
        raise http_error
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "check_user_status_exception", f"Unexpected exception in check_user_status: {email}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail=f"Error checking user status: {str(e)}")
