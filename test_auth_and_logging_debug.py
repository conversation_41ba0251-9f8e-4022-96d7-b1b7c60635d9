#!/usr/bin/env python3
"""
Debug script to test authentication and logging issues
"""
import asyncio
import uuid
import sys
import os
import httpx
from dotenv import load_dotenv

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'latent-backend'))

# Load environment variables
load_dotenv('latent-backend/.env')

async def test_supabase_connection():
    """Test direct Supabase connection"""
    print("=== Testing Supabase Connection ===")
    
    SUPABASE_URL = os.getenv("SUPABASE_URL")
    SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY")
    SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")
    
    print(f"SUPABASE_URL: {'✅ Set' if SUPABASE_URL else '❌ Missing'}")
    print(f"SUPABASE_PUBLIC_KEY: {'✅ Set' if SUPABASE_PUBLIC_KEY else '❌ Missing'}")
    print(f"SUPABASE_SERVICE_KEY: {'✅ Set' if SUPABASE_SERVICE_KEY else '❌ Missing'}")
    
    if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
        print("❌ Missing required Supabase configuration")
        return False
    
    # Test with public key
    print("\n--- Testing with PUBLIC KEY ---")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/user_requests",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                params={"select": "id", "limit": "1"}
            )
            print(f"Public key test: {response.status_code} - {response.text[:100]}")
    except Exception as e:
        print(f"Public key test failed: {e}")
    
    # Test with service key if available
    if SUPABASE_SERVICE_KEY:
        print("\n--- Testing with SERVICE KEY ---")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/user_requests",
                    headers={
                        "apikey": SUPABASE_SERVICE_KEY,
                        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                        "Content-Type": "application/json"
                    },
                    params={"select": "id", "limit": "1"}
                )
                print(f"Service key test: {response.status_code} - {response.text[:100]}")
        except Exception as e:
            print(f"Service key test failed: {e}")
    
    return True

async def test_logging_functions():
    """Test the logging functions"""
    print("\n=== Testing Logging Functions ===")
    
    try:
        from app.services.error_logger import log_user_request, log_interaction
        
        # Generate a test request ID
        test_request_id = str(uuid.uuid4())
        print(f"Testing with request_id: {test_request_id}")
        
        # Test log_user_request
        print("\n--- Testing log_user_request ---")
        await log_user_request(
            user_id="debug_user",
            user_email="<EMAIL>",
            ip_address="127.0.0.1",
            endpoint="/debug/test",
            method="GET",
            request_params={"debug": True},
            request_id=test_request_id
        )
        print("✅ log_user_request completed")
        
        # Test log_interaction
        print("\n--- Testing log_interaction ---")
        await log_interaction(
            request_id=test_request_id,
            step="debug_step",
            description="Debug test step",
            params={"debug": True}
        )
        print("✅ log_interaction completed")
        
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_api_endpoint():
    """Test a simple API endpoint"""
    print("\n=== Testing API Endpoint ===")
    
    try:
        # Test the health check endpoint (no auth required)
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/gap-analysis/")
            print(f"Health check: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"API endpoint test failed: {e}")

async def main():
    """Run all diagnostic tests"""
    print("🔍 Starting Authentication and Logging Diagnostics\n")
    
    await test_supabase_connection()
    await test_logging_functions()
    await test_api_endpoint()
    
    print("\n🏁 Diagnostics completed!")

if __name__ == "__main__":
    asyncio.run(main())
