#!/usr/bin/env python3
"""
Check database tables to understand the current state
"""
import asyncio
import httpx
import os
from dotenv import load_dotenv

load_dotenv()

async def check_database():
    """Check the current state of the database"""
    
    SUPABASE_URL = os.getenv("SUPABASE_URL")
    SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
        print("❌ Missing environment variables")
        return
    
    headers = {
        "apikey": SUPABASE_SERVICE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient() as client:
        
        # Check organizations
        print("=== ORGANIZATIONS ===")
        try:
            response = await client.get(f"{SUPABASE_URL}/rest/v1/organizations", headers=headers)
            if response.status_code == 200:
                orgs = response.json()
                print(f"Found {len(orgs)} organizations:")
                for org in orgs:
                    print(f"  - {org.get('name', 'No name')} (ID: {org.get('id', 'No ID')})")
            else:
                print(f"Error fetching organizations: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Exception checking organizations: {e}")
        
        # Check allowed_emails
        print("\n=== ALLOWED EMAILS ===")
        try:
            response = await client.get(f"{SUPABASE_URL}/rest/v1/allowed_emails", headers=headers)
            if response.status_code == 200:
                emails = response.json()
                print(f"Found {len(emails)} allowed emails:")
                for email in emails:
                    print(f"  - {email.get('email', 'No email')} (Active: {email.get('is_active', False)}, Org: {email.get('organization_id', 'No org')})")
            else:
                print(f"Error fetching allowed emails: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Exception checking allowed emails: {e}")
        
        # Check users
        print("\n=== USERS ===")
        try:
            response = await client.get(f"{SUPABASE_URL}/rest/v1/users", headers=headers)
            if response.status_code == 200:
                users = response.json()
                print(f"Found {len(users)} users:")
                for user in users:
                    print(f"  - {user.get('email', 'No email')} (ID: {user.get('id', 'No ID')}, Org: {user.get('organization_id', 'No org')})")
            else:
                print(f"Error fetching users: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Exception checking users: {e}")
        
        # Check specific email
        print(f"\n=== CHECKING SPECIFIC EMAIL: <EMAIL> ===")
        try:
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/allowed_emails",
                headers=headers,
                params={"email": "<EMAIL>"}
            )
            if response.status_code == 200:
                email_data = response.json()
                if email_data:
                    print(f"✅ Email found in allowed_emails: {email_data[0]}")
                else:
                    print("❌ Email NOT found in allowed_emails table")
            else:
                print(f"Error checking specific email: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Exception checking specific email: {e}")

if __name__ == "__main__":
    asyncio.run(check_database())
