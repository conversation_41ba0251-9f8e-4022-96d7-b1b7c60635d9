#!/usr/bin/env python3
"""
Test script to verify the logging race condition fix
"""
import asyncio
import uuid
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'latent-backend'))

async def test_race_condition_fix():
    """Test the race condition fix for log_interaction"""
    try:
        from app.services.error_logger import log_user_request, log_interaction
        
        # Generate a test request ID
        test_request_id = str(uuid.uuid4())
        print(f"Testing with request_id: {test_request_id}")
        
        print("\n=== Test 1: Normal sequence (should work) ===")
        
        # Log the user request
        await log_user_request(
            user_id="test_user",
            user_email="<EMAIL>",
            ip_address="127.0.0.1",
            endpoint="/api/regulations/organization",
            method="GET",
            request_params={"user_id": "test_user"},
            request_id=test_request_id
        )
        print("✅ User request logged")
        
        # Log interaction (should work now with the check)
        await log_interaction(
            request_id=test_request_id,
            step="test_step_1",
            description="First test step",
            params={"test": "param1"}
        )
        print("✅ First interaction logged")
        
        # Test multiple rapid interactions
        tasks = []
        for i in range(5):
            task = asyncio.create_task(log_interaction(
                request_id=test_request_id,
                step=f"rapid_test_{i}",
                description=f"Rapid test step {i}",
                params={"test": f"param{i}"}
            ))
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        print("✅ Multiple rapid interactions logged successfully")
        
        print("\n=== Test 2: Invalid request_id (should be handled gracefully) ===")
        
        # Test with non-existent request_id
        invalid_request_id = str(uuid.uuid4())
        await log_interaction(
            request_id=invalid_request_id,
            step="invalid_test",
            description="Test with invalid request_id",
            params={"test": "should_be_skipped"}
        )
        print("✅ Invalid request_id handled gracefully (should be skipped)")
        
        print("\n🎉 All tests passed! The race condition fix is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing the logging race condition fix...")
    asyncio.run(test_race_condition_fix())
